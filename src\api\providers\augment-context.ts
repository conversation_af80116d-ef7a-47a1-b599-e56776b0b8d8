import OpenAI from "openai"
import type { Stream } from "openai/streaming"
import { Anthropic } from "@anthropic-ai/sdk"

import type { ModelInfo } from "@roo-code/types"
import type { ApiHandlerOptions } from "../../shared/api"

import { ApiStream } from "../transform/stream"
import { getModelParams } from "../transform/model-params"

import { BaseProvider } from "./base-provider"
import type { SingleCompletionHandler, ApiHandlerCreateMessageMetadata } from "../index"
import { calculateApiCostAnthropic } from "../../shared/cost"
import { AugmentContextManager } from "../../services/augment-context/manager"
import { MCPIntegrationService } from "../../services/mcp/integration"

import { MemoryManager, MemoryContext, MemoryScope } from "../../services/agent-memories"
import { EmbeddingOptions } from "../../services/augment-context/embedding-service"

// Default model configurations
const coreDriverDefaultModelId = "claude-3-7-sonnet-20250219"
const coreDriverDefaultModelInfo: ModelInfo = {
	maxTokens: 8192,
	contextWindow: 200_000,
	supportsImages: true,
	supportsComputerUse: true,
	supportsPromptCache: true,
	inputPrice: 3.0,
	outputPrice: 15.0,
	cacheWritesPrice: 3.75,
	cacheReadsPrice: 0.3,
}

const ensemblerDefaultModelId = "o1"
const ensemblerDefaultModelInfo: ModelInfo = {
	maxTokens: 100_000,
	contextWindow: 200_000,
	supportsImages: true,
	supportsPromptCache: true,
	inputPrice: 15,
	outputPrice: 60,
	cacheReadsPrice: 7.5,
}



export class AugmentContextHandler extends BaseProvider implements SingleCompletionHandler {
	private options: ApiHandlerOptions
	private coreDriverClient: OpenAI
	private ensemblerClient: OpenAI
	private contextEngineEnabled: boolean
	private ensemblerEnabled: boolean
	private contextManager: AugmentContextManager
	private mcpIntegration: MCPIntegrationService
	private memoryManager: MemoryManager

	constructor(options: ApiHandlerOptions) {
		super()
		this.options = options

		// Initialize core driver (Claude Sonnet 3.7) - using OpenAI-compatible format
		const coreDriverApiKey = options.coreDriverApiKey || options.apiKey
		this.coreDriverClient = new OpenAI({
			baseURL: options.coreDriverBaseUrl || "http://localhost:3001/v1",
			apiKey: coreDriverApiKey,
		})

		// Initialize ensembler (OpenAI o1) - using OpenAI-compatible format
		const ensemblerApiKey = options.ensemblerApiKey || options.openAiApiKey
		this.ensemblerClient = new OpenAI({
			baseURL: options.ensemblerBaseUrl || "http://localhost:3002/v1",
			apiKey: ensemblerApiKey,
		})

		this.contextEngineEnabled = options.contextEngineEnabled ?? true
		this.ensemblerEnabled = options.ensemblerEnabled ?? true

		// Initialize context manager with embedding options
		const embeddingOptions: EmbeddingOptions = {
			embeddingApiKey: options.embeddingApiKey,
			embeddingBaseUrl: options.embeddingBaseUrl,
			embeddingModelId: options.embeddingModelId,
			embeddingProvider: options.embeddingProvider,
		}
		this.contextManager = new AugmentContextManager(embeddingOptions)
		this.mcpIntegration = new MCPIntegrationService(this.contextManager)
		this.memoryManager = new MemoryManager({
			enableAutoLearning: options.agentMemoriesEnabled ?? true,
			adaptationEnabled: options.agentMemoriesEnabled ?? true,
		})

		// Initialize services if enabled
		if (options.mcpIntegrationEnabled ?? true) {
			this.initializeMCPIntegration()
		}

		if (options.agentMemoriesEnabled ?? true) {
			this.initializeMemoryManager()
		}
	}

	async *createMessage(
		systemPrompt: string,
		messages: Anthropic.Messages.MessageParam[],
		metadata?: ApiHandlerCreateMessageMetadata,
	): ApiStream {
		// Implement dual-model architecture
		if (this.ensemblerEnabled && this.shouldUseEnsembler(systemPrompt, messages)) {
			yield* this.createDualModelMessage(systemPrompt, messages, metadata)
		} else {
			// Use core driver only for simple tasks
			yield* this.createCoreDriverMessage(systemPrompt, messages)
		}
	}







	private async *createCoreDriverMessage(
		systemPrompt: string,
		messages: Anthropic.Messages.MessageParam[],
	): ApiStream {
		const { id: modelId, info: modelInfo } = this.getCoreDriverModel()

		// Apply context enhancement if enabled
		if (this.contextEngineEnabled) {
			systemPrompt = await this.enhanceWithContext(systemPrompt, messages)
		}

		// Apply personalization if enabled
		if (this.options.agentMemoriesEnabled ?? true) {
			systemPrompt = await this.applyPersonalization(systemPrompt, messages)
		}

		const params = getModelParams({
			format: "openai",
			modelId,
			model: modelInfo,
			settings: this.options,
			defaultTemperature: 0,
		})

		const { maxTokens, temperature } = params

		// Convert to OpenAI chat completions format
		const openAiMessages = this.convertToOpenAiMessages(systemPrompt, messages)

		try {
			const stream = await this.coreDriverClient.chat.completions.create({
				model: modelId,
				max_tokens: maxTokens || 4096,
				temperature: temperature || 0,
				messages: openAiMessages,
				stream: true,
			});

			let totalInputTokens = 0
			let totalOutputTokens = 0
			let totalCacheCreationInputTokens = 0
			let totalCacheReadInputTokens = 0
			let responseText = ""

			for await (const chunk of stream) {
				if (chunk.choices && chunk.choices[0]) {
					const choice = chunk.choices[0]
					if (choice.delta?.content) {
						responseText += choice.delta.content
						yield {
							type: "text",
							text: choice.delta.content,
						}
					}
				}

				// Handle usage information if available
				if (chunk.usage) {
					totalInputTokens = chunk.usage.prompt_tokens || 0
					totalOutputTokens = chunk.usage.completion_tokens || 0
					// OpenAI doesn't provide cache tokens in the same way, so we'll set them to 0
					totalCacheCreationInputTokens = 0
					totalCacheReadInputTokens = 0
				}
			}

			// Learn from the interaction
			if (this.options.agentMemoriesEnabled ?? true) {
				this.learnFromInteraction(systemPrompt, messages, responseText)
					.catch(error => console.error("Failed to learn from interaction:", error))
			}

			const cost = calculateApiCostAnthropic(
				this.getCoreDriverModel().info,
				totalInputTokens,
				totalOutputTokens,
				totalCacheCreationInputTokens,
				totalCacheReadInputTokens,
			)

			yield {
				type: "usage",
				inputTokens: totalInputTokens,
				outputTokens: totalOutputTokens,
				cacheWriteTokens: totalCacheCreationInputTokens,
				cacheReadTokens: totalCacheReadInputTokens,
				totalCost: cost,
			}
		} catch (error) {
			console.error("Core driver error:", error)
			throw error
		}
	}

	/**
	 * Determine if the ensembler should be used for this request
	 * Use ensembler for complex tasks that benefit from verification and refinement
	 */
	private shouldUseEnsembler(
		systemPrompt: string,
		messages: Anthropic.Messages.MessageParam[],
	): boolean {
		// Extract the full conversation text
		const fullText = systemPrompt + " " + messages.map(m =>
			typeof m.content === 'string' ? m.content :
			Array.isArray(m.content) ? m.content.map(c => c.type === 'text' ? c.text : '').join(' ') : ''
		).join(' ')

		const lowerText = fullText.toLowerCase()

		// Use ensembler for complex coding tasks
		const complexCodingKeywords = [
			"implement", "create", "build", "develop", "refactor", "optimize",
			"add feature", "jwt", "authentication", "api", "database", "security",
			"multi-step", "multiple files", "architecture", "design pattern",
			"test", "testing", "deployment", "performance", "scalability"
		]

		// Use ensembler for planning and decision-making tasks
		const planningKeywords = [
			"plan", "strategy", "approach", "steps", "workflow", "process",
			"best practice", "recommend", "suggest", "compare", "evaluate",
			"pros and cons", "trade-offs", "alternatives"
		]

		// Use ensembler for code review and quality tasks
		const qualityKeywords = [
			"review", "check", "verify", "validate", "improve", "fix",
			"bug", "error", "issue", "problem", "debug", "troubleshoot",
			"clean up", "style", "convention", "standard"
		]

		const allKeywords = [...complexCodingKeywords, ...planningKeywords, ...qualityKeywords]

		return allKeywords.some(keyword => lowerText.includes(keyword))
	}

	/**
	 * Create message using dual-model architecture
	 * Core Driver generates initial response, Ensembler reviews and refines it
	 */
	private async *createDualModelMessage(
		systemPrompt: string,
		messages: Anthropic.Messages.MessageParam[],
		metadata?: ApiHandlerCreateMessageMetadata,
	): ApiStream {
		try {
			// Step 1: Generate initial response with Core Driver
			const coreResponse = await this.generateCoreResponse(systemPrompt, messages)

			// Step 2: Review and refine with Ensembler
			const refinedResponse = await this.refineWithEnsembler(systemPrompt, messages, coreResponse)

			// Step 3: Stream the refined response
			yield* this.streamRefinedResponse(refinedResponse, systemPrompt, messages)

		} catch (error) {
			console.error("Dual-model processing failed, falling back to core driver:", error)
			yield* this.createCoreDriverMessage(systemPrompt, messages)
		}
	}

	private convertToOpenAiMessages(
		systemPrompt: string,
		messages: Anthropic.Messages.MessageParam[],
	): OpenAI.Chat.Completions.ChatCompletionMessageParam[] {
		const openAiMessages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = []

		if (systemPrompt) {
			openAiMessages.push({ role: "system", content: systemPrompt })
		}

		for (const message of messages) {
			if (typeof message.content === "string") {
				openAiMessages.push({
					role: message.role as "user" | "assistant",
					content: message.content,
				})
			} else if (Array.isArray(message.content)) {
				const textContent = message.content
					.filter(c => c.type === "text")
					.map(c => (c as any).text)
					.join("\n")
				
				if (textContent) {
					openAiMessages.push({
						role: message.role as "user" | "assistant",
						content: textContent,
					})
				}
			}
		}

		return openAiMessages
	}

	private async enhanceWithContext(
		systemPrompt: string,
		messages: Anthropic.Messages.MessageParam[],
	): Promise<string> {
		try {
			// Extract query from the conversation
			const query = this.extractQueryFromMessages(systemPrompt, messages)

			if (!query) {
				return systemPrompt
			}

			// Retrieve enhanced context
			const contextResult = await this.contextManager.retrieveEnhancedContext({
				query,
				maxResults: 30,
				includeCommitHistory: true,
				includeRelatedCommits: true,
				contextWindowSize: this.options.contextWindowSize || 200_000,
			})

			// Build enhanced system prompt with context
			let enhancedPrompt = systemPrompt

			if (contextResult.codeContext.length > 0) {
				enhancedPrompt += "\n\n## Relevant Code Context\n"
				for (const context of contextResult.codeContext) {
					enhancedPrompt += `\n### ${context.filePath || 'Code Snippet'}\n`
					if (context.metadata?.startLine) {
						enhancedPrompt += `Lines ${context.metadata.startLine}-${context.metadata.endLine}\n`
					}
					enhancedPrompt += `\`\`\`${context.metadata?.language || 'text'}\n${context.content}\n\`\`\`\n`
				}
			}

			if (contextResult.commitContext.length > 0) {
				enhancedPrompt += "\n\n## Relevant Commit History\n"
				for (const commit of contextResult.commitContext) {
					enhancedPrompt += `\n### Commit ${commit.shortHash} by ${commit.author} (${commit.date})\n`
					enhancedPrompt += `**${commit.subject}**\n`
					if (commit.summary !== commit.subject) {
						enhancedPrompt += `${commit.summary}\n`
					}
					enhancedPrompt += `Files changed: ${commit.filesChanged.length}, +${commit.linesAdded}/-${commit.linesDeleted}\n`
				}
			}

			// Add context metadata
			enhancedPrompt += `\n\n## Context Metadata\n`
			enhancedPrompt += `- Retrieved ${contextResult.sources.codeIndex} code snippets\n`
			enhancedPrompt += `- Retrieved ${contextResult.sources.commitHistory} commit summaries\n`
			enhancedPrompt += `- Total context tokens: ~${contextResult.totalTokens}\n`
			enhancedPrompt += `- Retrieval time: ${contextResult.retrievalTime}ms\n`

			return enhancedPrompt

		} catch (error) {
			console.error("Failed to enhance context:", error)
			return systemPrompt
		}
	}

	private extractQueryFromMessages(
		systemPrompt: string,
		messages: Anthropic.Messages.MessageParam[],
	): string | null {
		// Extract the most recent user message as the query
		const lastUserMessage = messages
			.slice()
			.reverse()
			.find(m => m.role === "user")

		if (!lastUserMessage) {
			return null
		}

		if (typeof lastUserMessage.content === "string") {
			return lastUserMessage.content
		}

		if (Array.isArray(lastUserMessage.content)) {
			const textContent = lastUserMessage.content
				.filter(c => c.type === "text")
				.map(c => (c as any).text)
				.join(" ")
			return textContent || null
		}

		return null
	}

	getCoreDriverModel(): { id: string; info: ModelInfo } {
		const id = this.options.coreDriverModelId ?? coreDriverDefaultModelId
		return { id, info: coreDriverDefaultModelInfo }
	}

	getEnsemblerModel(): { id: string; info: ModelInfo } {
		const id = this.options.ensemblerModelId ?? ensemblerDefaultModelId
		return { id, info: ensemblerDefaultModelInfo }
	}



	override getModel(): { id: string; info: ModelInfo } {
		// Return core driver model as default
		return this.getCoreDriverModel()
	}



	/**
	 * Extract user message from messages array
	 */
	private extractUserMessage(messages: Anthropic.Messages.MessageParam[]): string {
		const lastUserMessage = messages
			.slice()
			.reverse()
			.find(m => m.role === "user")

		if (!lastUserMessage) {
			return ""
		}

		if (typeof lastUserMessage.content === "string") {
			return lastUserMessage.content
		}

		if (Array.isArray(lastUserMessage.content)) {
			return lastUserMessage.content
				.filter(c => c.type === "text")
				.map(c => (c as any).text)
				.join(" ")
		}

		return ""
	}

	/**
	 * Initialize MCP integration
	 */
	private async initializeMCPIntegration(): Promise<void> {
		try {
			await this.mcpIntegration.initialize({
				enableGitHub: true,
				enableFileSystem: true,
				enableWebSearch: true,
				githubToken: process.env.GITHUB_TOKEN,
			})
		} catch (error) {
			console.error("Failed to initialize MCP integration:", error)
		}
	}

	/**
	 * Initialize memory manager
	 */
	private async initializeMemoryManager(): Promise<void> {
		try {
			await this.memoryManager.initialize()
		} catch (error) {
			console.error("Failed to initialize memory manager:", error)
		}
	}

	/**
	 * Learn from user interaction and adapt behavior
	 */
	private async learnFromInteraction(
		systemPrompt: string,
		messages: Anthropic.Messages.MessageParam[],
		response: string
	): Promise<void> {
		try {
			// Extract context from the interaction
			const context: MemoryContext = {
				workspace: this.getWorkspaceName(),
				scope: MemoryScope.WORKSPACE,
			}

			// Detect language/framework from messages
			const userMessage = this.extractUserMessage(messages)
			const languageInfo = this.detectLanguageFromContent(userMessage + " " + response)

			if (languageInfo.language) {
				context.language = languageInfo.language
				context.framework = languageInfo.framework
			}

			// Learn from the interaction
			await this.memoryManager.learnFromInteraction(
				"ai_response",
				context,
				{
					systemPrompt: systemPrompt.substring(0, 500), // Truncate for storage
					userMessage: userMessage.substring(0, 500),
					response: response.substring(0, 500),
					modelUsed: this.getCoreDriverModel().id,
				}
			)

			// Learn coding style if code is present
			if (this.containsCode(response) && languageInfo.language) {
				await this.memoryManager.learnCodingStyle(
					response,
					languageInfo.language,
					context
				)
			}

		} catch (error) {
			console.error("Failed to learn from interaction:", error)
		}
	}

	/**
	 * Apply personalization to system prompt
	 */
	private async applyPersonalization(
		systemPrompt: string,
		messages: Anthropic.Messages.MessageParam[]
	): Promise<string> {
		try {
			// Extract context
			const userMessage = this.extractUserMessage(messages)
			const languageInfo = this.detectLanguageFromContent(userMessage)

			const context: MemoryContext = {
				workspace: this.getWorkspaceName(),
				language: languageInfo.language,
				framework: languageInfo.framework,
				scope: MemoryScope.WORKSPACE,
			}

			// Get relevant memories
			const relevantMemories = await this.memoryManager.getRelevantMemories(context, 10)

			if (relevantMemories.length === 0) {
				return systemPrompt
			}

			// Apply adaptations
			const adaptations = await this.memoryManager.applyAdaptations(context)

			// Enhance system prompt with personalization
			let enhancedPrompt = systemPrompt

			// Add coding style preferences
			if (languageInfo.language) {
				const stylePreferences = await this.memoryManager.getCodingStylePreferences(languageInfo.language)
				if (stylePreferences.length > 0) {
					enhancedPrompt += "\n\n## Coding Style Preferences\n"
					for (const pref of stylePreferences.slice(0, 3)) {
						enhancedPrompt += `- ${pref.content}\n`
					}
				}
			}

			// Add user preferences
			const preferences = await this.memoryManager.getPreferences()
			if (preferences.length > 0) {
				enhancedPrompt += "\n\n## User Preferences\n"
				for (const pref of preferences.slice(0, 5)) {
					enhancedPrompt += `- ${pref.content}\n`
				}
			}

			// Add adaptation notes
			if (adaptations.length > 0) {
				enhancedPrompt += `\n\n## Applied Adaptations\n`
				enhancedPrompt += `Applied: ${adaptations.join(", ")}\n`
			}

			return enhancedPrompt

		} catch (error) {
			console.error("Failed to apply personalization:", error)
			return systemPrompt
		}
	}

	/**
	 * Detect language and framework from content
	 */
	private detectLanguageFromContent(content: string): { language?: string; framework?: string } {
		const contentLower = content.toLowerCase()

		// Simple language detection
		if (contentLower.includes("typescript") || contentLower.includes(".ts")) {
			return { language: "typescript", framework: this.detectFramework(contentLower, "typescript") }
		}
		if (contentLower.includes("javascript") || contentLower.includes(".js")) {
			return { language: "javascript", framework: this.detectFramework(contentLower, "javascript") }
		}
		if (contentLower.includes("python") || contentLower.includes(".py")) {
			return { language: "python", framework: this.detectFramework(contentLower, "python") }
		}
		if (contentLower.includes("java") || contentLower.includes(".java")) {
			return { language: "java" }
		}

		return {}
	}

	/**
	 * Detect framework from content and language
	 */
	private detectFramework(content: string, language: string): string | undefined {
		if (language === "typescript" || language === "javascript") {
			if (content.includes("react")) return "react"
			if (content.includes("vue")) return "vue"
			if (content.includes("angular")) return "angular"
			if (content.includes("express")) return "express"
		}

		if (language === "python") {
			if (content.includes("django")) return "django"
			if (content.includes("flask")) return "flask"
			if (content.includes("fastapi")) return "fastapi"
		}

		return undefined
	}

	/**
	 * Check if content contains code
	 */
	private containsCode(content: string): boolean {
		// Simple heuristic to detect code
		return /```|function|class|def |import |from |const |let |var /.test(content)
	}

	/**
	 * Get workspace name
	 */
	private getWorkspaceName(): string {
		// This would get the actual workspace name
		return "current-workspace"
	}

	/**
	 * Generate initial response using Core Driver
	 */
	private async generateCoreResponse(
		systemPrompt: string,
		messages: Anthropic.Messages.MessageParam[],
	): Promise<string> {
		// Apply context enhancement if enabled
		if (this.contextEngineEnabled) {
			systemPrompt = await this.enhanceWithContext(systemPrompt, messages)
		}

		// Apply personalization if enabled
		if (this.options.agentMemoriesEnabled ?? true) {
			systemPrompt = await this.applyPersonalization(systemPrompt, messages)
		}

		const { id: modelId } = this.getCoreDriverModel()
		const openAiMessages = this.convertToOpenAiMessages(systemPrompt, messages)

		const response = await this.coreDriverClient.chat.completions.create({
			model: modelId,
			max_tokens: 4096,
			temperature: 0,
			messages: openAiMessages,
		})

		return response.choices[0]?.message?.content || ""
	}

	/**
	 * Refine Core Driver response using Ensembler
	 */
	private async refineWithEnsembler(
		systemPrompt: string,
		messages: Anthropic.Messages.MessageParam[],
		coreResponse: string,
	): Promise<string> {
		const { id: modelId } = this.getEnsemblerModel()

		// Create ensembler prompt for review and refinement
		const ensemblerPrompt = this.createEnsemblerPrompt(systemPrompt, messages, coreResponse)
		const ensemblerMessages = this.convertToOpenAiMessages(ensemblerPrompt, [])

		const response = await this.ensemblerClient.chat.completions.create({
			model: modelId,
			max_tokens: 4096,
			temperature: 0.3,
			messages: ensemblerMessages,
		})

		return response.choices[0]?.message?.content || coreResponse
	}

	/**
	 * Create specialized prompt for Ensembler to review and refine Core's output
	 */
	private createEnsemblerPrompt(
		originalSystemPrompt: string,
		messages: Anthropic.Messages.MessageParam[],
		coreResponse: string,
	): string {
		const userMessage = this.extractUserMessage(messages)

		return `You are an AI Ensembler responsible for reviewing, verifying, and refining responses from a Core Driver model.

## Your Role:
- Review the Core Driver's response for correctness, completeness, and quality
- Verify that the response addresses the user's request appropriately
- Identify and fix any hallucinations, errors, or inconsistencies
- Improve structure, clarity, and adherence to best practices
- Ensure coding conventions and security standards are followed
- Reorder or restructure steps if needed for better logical flow

## Original User Request:
${userMessage}

## Original System Context:
${originalSystemPrompt}

## Core Driver's Response:
${coreResponse}

## Your Task:
Review the Core Driver's response and provide an improved version. If the response is already excellent, you may return it unchanged. Focus on:

1. **Correctness**: Fix any technical errors or inaccuracies
2. **Completeness**: Add missing steps, considerations, or explanations
3. **Structure**: Improve organization and logical flow
4. **Quality**: Enhance clarity, readability, and best practices
5. **Safety**: Ensure security and performance considerations are addressed

Provide your refined response below:`
	}

	/**
	 * Stream the refined response with proper token tracking
	 */
	private async *streamRefinedResponse(
		refinedResponse: string,
		systemPrompt: string,
		messages: Anthropic.Messages.MessageParam[],
	): ApiStream {
		// Stream the refined response in chunks
		const chunkSize = 50
		for (let i = 0; i < refinedResponse.length; i += chunkSize) {
			const chunk = refinedResponse.slice(i, i + chunkSize)
			yield {
				type: "text",
				text: chunk,
			}
			// Small delay to simulate streaming
			await new Promise(resolve => setTimeout(resolve, 10))
		}

		// Learn from the interaction
		if (this.options.agentMemoriesEnabled ?? true) {
			this.learnFromInteraction(systemPrompt, messages, refinedResponse)
				.catch(error => console.error("Failed to learn from interaction:", error))
		}

		// Estimate token usage (simplified)
		const inputTokens = Math.ceil((systemPrompt + JSON.stringify(messages)).length / 4)
		const outputTokens = Math.ceil(refinedResponse.length / 4)

		const coreDriverCost = calculateApiCostAnthropic(
			this.getCoreDriverModel().info,
			inputTokens,
			outputTokens,
			0,
			0,
		)

		const ensemblerCost = calculateApiCostAnthropic(
			this.getEnsemblerModel().info,
			inputTokens,
			outputTokens,
			0,
			0,
		)

		yield {
			type: "usage",
			inputTokens: inputTokens * 2, // Both models process input
			outputTokens,
			cacheWriteTokens: 0,
			cacheReadTokens: 0,
			totalCost: coreDriverCost + ensemblerCost,
		}
	}

	/**
	 * Implementation of SingleCompletionHandler interface
	 * Provides a simple completion method for single prompts
	 */
	async completePrompt(prompt: string): Promise<string> {
		try {
			// Use the core driver for simple completions
			const response = await this.coreDriverClient.chat.completions.create({
				model: this.getCoreDriverModel().id,
				max_tokens: 4096,
				messages: [{ role: "user", content: prompt }],
			})

			// Extract text content from the response
			return response.choices[0]?.message?.content || ""
		} catch (error) {
			console.error("Augment Context completion error:", error)
			throw new Error(`Augment Context completion failed: ${error instanceof Error ? error.message : String(error)}`)
		}
	}
}
