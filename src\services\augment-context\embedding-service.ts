import OpenAI from "openai"
import { ApiHandlerOptions } from "../../shared/api"

export interface EmbeddingOptions {
	embeddingApiKey?: string
	embeddingBaseUrl?: string
	embeddingModelId?: string
	embeddingProvider?: "openai" | "openai-compatible" | "custom"
}

export interface EmbeddingResponse {
	embeddings: number[][]
	usage?: {
		promptTokens: number
		totalTokens: number
	}
}

export interface EmbeddingValidationResult {
	valid: boolean
	error?: string
	modelDimension?: number
}

/**
 * OpenAI-compatible embedding service for the Augment Context Engine
 * Provides semantic indexing capabilities with configurable providers
 */
export class AugmentEmbeddingService {
	private client: OpenAI
	private modelId: string
	private provider: string
	private maxBatchSize: number = 100
	private maxTokensPerItem: number = 8192

	constructor(options: EmbeddingOptions) {
		const {
			embeddingApiKey = "",
			embeddingBaseUrl = "https://api.openai.com/v1",
			embeddingModelId = "text-embedding-3-small",
			embeddingProvider = "openai"
		} = options

		if (!embeddingApiKey) {
			throw new Error("Embedding API key is required")
		}

		this.client = new OpenAI({
			baseURL: embeddingBaseUrl,
			apiKey: embeddingApiKey,
		})

		this.modelId = embeddingModelId
		this.provider = embeddingProvider
	}

	/**
	 * Create embeddings for the given texts using OpenAI-compatible API
	 */
	async createEmbeddings(texts: string[], model?: string): Promise<EmbeddingResponse> {
		if (!texts || texts.length === 0) {
			return { embeddings: [] }
		}

		const modelToUse = model || this.modelId
		const batches = this.createBatches(texts)
		const allEmbeddings: number[][] = []
		let totalPromptTokens = 0
		let totalTokens = 0

		for (const batch of batches) {
			try {
				const response = await this.client.embeddings.create({
					input: batch,
					model: modelToUse,
					// Request base64 encoding to handle high-dimensional embeddings properly
					encoding_format: "base64",
				})

				// Process base64 embeddings
				const processedEmbeddings = response.data.map((item) => {
					if (typeof item.embedding === "string") {
						// Decode base64 to float32 array
						const buffer = Buffer.from(item.embedding, "base64")
						const float32Array = new Float32Array(
							buffer.buffer,
							buffer.byteOffset,
							buffer.byteLength / 4
						)
						return Array.from(float32Array)
					}
					return item.embedding as number[]
				})

				allEmbeddings.push(...processedEmbeddings)

				// Track usage
				if (response.usage) {
					totalPromptTokens += response.usage.prompt_tokens
					totalTokens += response.usage.total_tokens
				}

			} catch (error) {
				console.error("Failed to create embeddings for batch:", error)
				throw new Error(`Embedding generation failed: ${error instanceof Error ? error.message : String(error)}`)
			}
		}

		return {
			embeddings: allEmbeddings,
			usage: {
				promptTokens: totalPromptTokens,
				totalTokens: totalTokens,
			},
		}
	}

	/**
	 * Validate the embedding configuration by testing connectivity
	 */
	async validateConfiguration(): Promise<EmbeddingValidationResult> {
		try {
			const testTexts = ["Hello, world!", "This is a test embedding."]
			
			const response = await this.client.embeddings.create({
				input: testTexts,
				model: this.modelId,
				encoding_format: "base64",
			})

			if (!response?.data || response.data.length === 0) {
				return {
					valid: false,
					error: "Invalid response from embedding API",
				}
			}

			// Determine model dimension from the first embedding
			let modelDimension: number | undefined
			const firstEmbedding = response.data[0]?.embedding

			if (typeof firstEmbedding === "string") {
				// Base64 encoded - decode to get dimension
				const buffer = Buffer.from(firstEmbedding, "base64")
				modelDimension = buffer.byteLength / 4 // 4 bytes per float32
			} else if (Array.isArray(firstEmbedding)) {
				modelDimension = firstEmbedding.length
			}

			return {
				valid: true,
				modelDimension,
			}

		} catch (error) {
			return {
				valid: false,
				error: error instanceof Error ? error.message : String(error),
			}
		}
	}

	/**
	 * Get embedding service information
	 */
	getServiceInfo() {
		return {
			provider: this.provider,
			modelId: this.modelId,
			maxBatchSize: this.maxBatchSize,
			maxTokensPerItem: this.maxTokensPerItem,
		}
	}

	/**
	 * Estimate token count for text (rough approximation)
	 */
	estimateTokenCount(text: string): number {
		// Rough estimation: ~4 characters per token
		return Math.ceil(text.length / 4)
	}

	/**
	 * Split texts into batches for efficient processing
	 */
	private createBatches(texts: string[]): string[][] {
		const batches: string[][] = []
		let currentBatch: string[] = []
		let currentBatchTokens = 0

		for (const text of texts) {
			const textTokens = this.estimateTokenCount(text)
			
			// Skip texts that are too long
			if (textTokens > this.maxTokensPerItem) {
				console.warn(`Skipping text with ${textTokens} tokens (exceeds limit of ${this.maxTokensPerItem})`)
				continue
			}

			// Start new batch if current would exceed limits
			if (currentBatch.length >= this.maxBatchSize || 
				(currentBatchTokens + textTokens > this.maxTokensPerItem * this.maxBatchSize)) {
				if (currentBatch.length > 0) {
					batches.push(currentBatch)
					currentBatch = []
					currentBatchTokens = 0
				}
			}

			currentBatch.push(text)
			currentBatchTokens += textTokens
		}

		// Add final batch
		if (currentBatch.length > 0) {
			batches.push(currentBatch)
		}

		return batches
	}

	/**
	 * Calculate embedding cost (simplified estimation)
	 */
	calculateCost(tokenCount: number): number {
		// Default OpenAI pricing for text-embedding-3-small: $0.00002 per 1K tokens
		const pricePerThousandTokens = 0.00002
		return (tokenCount / 1000) * pricePerThousandTokens
	}
}
