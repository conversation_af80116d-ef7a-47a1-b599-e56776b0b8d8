import { describe, it, expect, vi, beforeEach } from "vitest"
import { AugmentEmbeddingService } from "../embedding-service"
import OpenAI from "openai"

// Mock OpenAI
vi.mock("openai")

describe("AugmentEmbeddingService", () => {
	let mockOpenAI: any
	let embeddingService: AugmentEmbeddingService

	beforeEach(() => {
		mockOpenAI = {
			embeddings: {
				create: vi.fn(),
			},
		}
		;(OpenAI as any).mockImplementation(() => mockOpenAI)

		embeddingService = new AugmentEmbeddingService({
			embeddingApiKey: "test-key",
			embeddingBaseUrl: "https://api.openai.com/v1",
			embeddingModelId: "text-embedding-3-small",
			embeddingProvider: "openai",
		})
	})

	describe("constructor", () => {
		it("should create instance with valid options", () => {
			expect(embeddingService).toBeDefined()
			expect(embeddingService.getServiceInfo()).toEqual({
				provider: "openai",
				modelId: "text-embedding-3-small",
				maxBatchSize: 100,
				maxTokensPerItem: 8192,
			})
		})

		it("should throw error without API key", () => {
			expect(() => {
				new AugmentEmbeddingService({
					embeddingBaseUrl: "https://api.openai.com/v1",
				})
			}).toThrow("Embedding API key is required")
		})
	})

	describe("createEmbeddings", () => {
		it("should create embeddings for texts", async () => {
			const mockResponse = {
				data: [
					{ embedding: "base64encodeddata1" },
					{ embedding: "base64encodeddata2" },
				],
				usage: {
					prompt_tokens: 10,
					total_tokens: 10,
				},
			}

			// Mock base64 decoding to return float arrays
			const mockBuffer = Buffer.from(new Float32Array([0.1, 0.2, 0.3, 0.4]).buffer)
			vi.spyOn(Buffer, "from").mockReturnValue(mockBuffer)

			mockOpenAI.embeddings.create.mockResolvedValue(mockResponse)

			const result = await embeddingService.createEmbeddings(["Hello", "World"])

			expect(mockOpenAI.embeddings.create).toHaveBeenCalledWith({
				input: ["Hello", "World"],
				model: "text-embedding-3-small",
				encoding_format: "base64",
			})

			expect(result).toEqual({
				embeddings: [
					[0.1, 0.2, 0.3, 0.4],
					[0.1, 0.2, 0.3, 0.4],
				],
				usage: {
					promptTokens: 10,
					totalTokens: 10,
				},
			})
		})

		it("should handle empty input", async () => {
			const result = await embeddingService.createEmbeddings([])
			expect(result).toEqual({ embeddings: [] })
		})

		it("should handle API errors", async () => {
			mockOpenAI.embeddings.create.mockRejectedValue(new Error("API Error"))

			await expect(embeddingService.createEmbeddings(["test"])).rejects.toThrow(
				"Embedding generation failed: API Error"
			)
		})
	})

	describe("validateConfiguration", () => {
		it("should validate successful configuration", async () => {
			const mockResponse = {
				data: [{ embedding: "base64encodeddata" }],
				usage: { prompt_tokens: 5, total_tokens: 5 },
			}

			const mockBuffer = Buffer.from(new Float32Array([0.1, 0.2, 0.3]).buffer)
			vi.spyOn(Buffer, "from").mockReturnValue(mockBuffer)

			mockOpenAI.embeddings.create.mockResolvedValue(mockResponse)

			const result = await embeddingService.validateConfiguration()

			expect(result).toEqual({
				valid: true,
				modelDimension: 3,
			})
		})

		it("should handle validation errors", async () => {
			mockOpenAI.embeddings.create.mockRejectedValue(new Error("Invalid API key"))

			const result = await embeddingService.validateConfiguration()

			expect(result).toEqual({
				valid: false,
				error: "Invalid API key",
			})
		})
	})

	describe("utility methods", () => {
		it("should estimate token count", () => {
			const tokenCount = embeddingService.estimateTokenCount("Hello world")
			expect(tokenCount).toBe(3) // ~11 chars / 4 = 2.75 -> 3
		})

		it("should calculate cost", () => {
			const cost = embeddingService.calculateCost(1000)
			expect(cost).toBe(0.00002) // 1000 tokens * $0.00002 per 1K tokens
		})
	})

	describe("batch processing", () => {
		it("should create batches for large inputs", async () => {
			// Create a large array of texts
			const largeTextArray = Array(150).fill("test text")

			const mockResponse = {
				data: Array(100).fill({ embedding: "base64data" }),
				usage: { prompt_tokens: 100, total_tokens: 100 },
			}

			const mockBuffer = Buffer.from(new Float32Array([0.1, 0.2]).buffer)
			vi.spyOn(Buffer, "from").mockReturnValue(mockBuffer)

			mockOpenAI.embeddings.create.mockResolvedValue(mockResponse)

			await embeddingService.createEmbeddings(largeTextArray)

			// Should be called twice due to batch size limit of 100
			expect(mockOpenAI.embeddings.create).toHaveBeenCalledTimes(2)
		})
	})
})
