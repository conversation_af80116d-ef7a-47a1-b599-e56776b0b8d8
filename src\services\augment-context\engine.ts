import * as vscode from "vscode"
import * as path from "path"
import { promises as fs } from "fs"
import { getWorkspacePath } from "../../utils/path"
import { CodeIndexManager } from "../code-index/manager"
import { VectorStoreSearchResult } from "../code-index/interfaces"
import { GitCommit, searchCommits, getCommitInfo } from "../../utils/git"
import { AugmentEmbeddingService } from "./embedding-service"

export interface ContextRetrievalOptions {
	query: string
	maxResults?: number
	includeCommitHistory?: boolean
	directoryPrefix?: string
	contextWindowSize?: number
}

export interface ContextResult {
	type: "code" | "commit" | "file"
	content: string
	filePath?: string
	score?: number
	metadata?: Record<string, any>
}

export interface WorkspaceIndexingStatus {
	isIndexing: boolean
	totalFiles: number
	indexedFiles: number
	lastIndexed?: Date
	error?: string
}

/**
 * Enhanced context engine similar to Augment Code's capabilities
 * Provides workspace indexing, context retrieval, and historical awareness
 */
export class AugmentContextEngine {
	private codeIndexManager: CodeIndexManager | undefined
	private embeddingService: AugmentEmbeddingService | undefined
	private workspacePath: string
	private contextWindowSize: number
	private maxRetrievalResults: number
	private isInitialized: boolean = false

	constructor(
		contextWindowSize: number = 200_000,
		maxRetrievalResults: number = 50,
		embeddingService?: AugmentEmbeddingService
	) {
		this.contextWindowSize = contextWindowSize
		this.maxRetrievalResults = maxRetrievalResults
		this.workspacePath = getWorkspacePath()
		this.embeddingService = embeddingService
		// CodeIndexManager will be initialized in initialize() method
	}

	/**
	 * Initialize the context engine
	 */
	async initialize(): Promise<void> {
		if (this.isInitialized) {
			return
		}

		try {
			// Get CodeIndexManager instance (requires extension context)
			// For now, we'll skip code indexing if no extension context is available
			const context = (global as any).extensionContext
			if (context) {
				this.codeIndexManager = CodeIndexManager.getInstance(context)
				if (this.codeIndexManager) {
					// Initialize with a mock context proxy for now
					const mockContextProxy = {
						get: (key: string) => undefined,
						set: (key: string, value: any) => Promise.resolve(),
					} as any
					await this.codeIndexManager.initialize(mockContextProxy)
				}
			}

			// Start automatic workspace indexing if available
			if (this.codeIndexManager) {
				await this.startWorkspaceIndexing()
			}

			this.isInitialized = true
		} catch (error) {
			console.error("Failed to initialize AugmentContextEngine:", error)
			// Don't throw error if code indexing fails, continue without it
			this.isInitialized = true
		}
	}

	/**
	 * Start automatic workspace indexing
	 */
	private async startWorkspaceIndexing(): Promise<void> {
		if (!this.codeIndexManager) {
			console.warn("CodeIndexManager not available, skipping workspace indexing")
			return
		}

		try {
			// Start indexing with the existing configuration
			// The CodeIndexManager handles its own configuration from VS Code settings
			await this.codeIndexManager.startIndexing()
		} catch (error) {
			console.error("Failed to start workspace indexing:", error)
			// Don't throw error, continue without indexing
		}
	}

	/**
	 * Retrieve relevant context for a given query
	 */
	async retrieveContext(options: ContextRetrievalOptions): Promise<ContextResult[]> {
		if (!this.isInitialized) {
			await this.initialize()
		}

		const results: ContextResult[] = []
		const maxResults = options.maxResults || this.maxRetrievalResults

		try {
			// 1. Search code index for relevant code snippets
			const codeResults = await this.searchCodeIndex(options.query, options.directoryPrefix)
			
			// Convert code search results to context results
			for (const result of codeResults.slice(0, Math.floor(maxResults * 0.7))) {
				results.push({
					type: "code",
					content: result.payload?.codeChunk || "",
					filePath: result.payload?.filePath || "",
					score: result.score,
					metadata: {
						startLine: result.payload?.startLine || 0,
						endLine: result.payload?.endLine || 0,
						language: this.detectLanguage(result.payload?.filePath || ""),
					},
				})
			}

			// 2. Include commit history if requested
			if (options.includeCommitHistory) {
				const commitResults = await this.searchCommitHistory(options.query)
				
				for (const commit of commitResults.slice(0, Math.floor(maxResults * 0.3))) {
					const commitInfo = await getCommitInfo(commit.hash, this.workspacePath)
					results.push({
						type: "commit",
						content: commitInfo,
						metadata: {
							hash: commit.hash,
							shortHash: commit.shortHash,
							author: commit.author,
							date: commit.date,
							subject: commit.subject,
						},
					})
				}
			}

			// 3. Rank and filter results by relevance
			const rankedResults = this.rankResults(results, options.query)
			
			// 4. Ensure results fit within context window
			return this.fitToContextWindow(rankedResults, options.contextWindowSize || this.contextWindowSize)

		} catch (error) {
			console.error("Failed to retrieve context:", error)
			return []
		}
	}

	/**
	 * Search the code index for relevant snippets
	 */
	private async searchCodeIndex(query: string, directoryPrefix?: string): Promise<VectorStoreSearchResult[]> {
		if (!this.codeIndexManager) {
			return []
		}

		try {
			return await this.codeIndexManager.searchIndex(query, directoryPrefix)
		} catch (error) {
			console.error("Failed to search code index:", error)
			return []
		}
	}

	/**
	 * Search commit history for relevant changes
	 */
	private async searchCommitHistory(query: string): Promise<GitCommit[]> {
		try {
			return await searchCommits(query, this.workspacePath)
		} catch (error) {
			console.error("Failed to search commit history:", error)
			return []
		}
	}

	/**
	 * Rank results by relevance to the query
	 */
	private rankResults(results: ContextResult[], query: string): ContextResult[] {
		const queryTerms = query.toLowerCase().split(/\s+/)
		
		return results
			.map(result => {
				let relevanceScore = result.score || 0
				
				// Boost score based on query term matches in content
				const content = result.content.toLowerCase()
				const matchCount = queryTerms.reduce((count, term) => {
					return count + (content.split(term).length - 1)
				}, 0)
				
				relevanceScore += matchCount * 0.1
				
				// Boost recent commits
				if (result.type === "commit" && result.metadata?.date) {
					const commitDate = new Date(result.metadata.date)
					const daysSinceCommit = (Date.now() - commitDate.getTime()) / (1000 * 60 * 60 * 24)
					relevanceScore += Math.max(0, 1 - daysSinceCommit / 30) * 0.2
				}
				
				return { ...result, score: relevanceScore }
			})
			.sort((a, b) => (b.score || 0) - (a.score || 0))
	}

	/**
	 * Fit results to context window size
	 */
	private fitToContextWindow(results: ContextResult[], contextWindowSize: number): ContextResult[] {
		const fitted: ContextResult[] = []
		let currentSize = 0
		
		// Rough estimation: 4 characters per token
		const maxSize = contextWindowSize * 4
		
		for (const result of results) {
			const resultSize = result.content.length
			
			if (currentSize + resultSize <= maxSize) {
				fitted.push(result)
				currentSize += resultSize
			} else {
				// Try to fit a truncated version
				const remainingSpace = maxSize - currentSize
				if (remainingSpace > 100) {
					fitted.push({
						...result,
						content: result.content.substring(0, remainingSpace - 50) + "...",
					})
				}
				break
			}
		}
		
		return fitted
	}

	/**
	 * Detect programming language from file path
	 */
	private detectLanguage(filePath: string): string {
		const ext = path.extname(filePath).toLowerCase()
		const languageMap: Record<string, string> = {
			".ts": "typescript",
			".js": "javascript",
			".tsx": "tsx",
			".jsx": "jsx",
			".py": "python",
			".java": "java",
			".cpp": "cpp",
			".c": "c",
			".cs": "csharp",
			".go": "go",
			".rs": "rust",
			".php": "php",
			".rb": "ruby",
			".swift": "swift",
			".kt": "kotlin",
			".scala": "scala",
			".sh": "bash",
			".sql": "sql",
			".json": "json",
			".yaml": "yaml",
			".yml": "yaml",
			".xml": "xml",
			".html": "html",
			".css": "css",
			".scss": "scss",
			".less": "less",
			".md": "markdown",
		}
		
		return languageMap[ext] || "text"
	}

	/**
	 * Get current workspace indexing status
	 */
	getIndexingStatus(): WorkspaceIndexingStatus {
		if (!this.codeIndexManager) {
			return {
				isIndexing: false,
				totalFiles: 0,
				indexedFiles: 0,
				lastIndexed: undefined,
				error: "CodeIndexManager not available",
			}
		}

		const status = this.codeIndexManager.getCurrentStatus()

		return {
			isIndexing: status.systemStatus === "Indexing",
			totalFiles: status.totalItems || 0,
			indexedFiles: status.processedItems || 0,
			lastIndexed: undefined, // This property doesn't exist in the status
			error: status.systemStatus === "Error" ? status.message : undefined,
		}
	}

	/**
	 * Clear all indexed data
	 */
	async clearIndex(): Promise<void> {
		if (this.codeIndexManager) {
			await this.codeIndexManager.clearIndexData()
		}
	}

	/**
	 * Enhance context retrieval with semantic search using embeddings
	 */
	async retrieveSemanticContext(
		query: string,
		maxResults: number = 20
	): Promise<ContextResult[]> {
		if (!this.embeddingService) {
			return []
		}

		try {
			// Get code files from workspace
			const codeFiles = await this.getWorkspaceCodeFiles()
			const codeChunks: string[] = []
			const chunkMetadata: Array<{ filePath: string; startLine: number; endLine: number }> = []

			// Extract code chunks from files
			for (const filePath of codeFiles.slice(0, 100)) { // Limit to prevent overwhelming
				try {
					const content = await fs.readFile(filePath, 'utf-8')
					const chunks = this.extractCodeChunks(content, filePath)

					chunks.forEach(chunk => {
						codeChunks.push(chunk.content)
						chunkMetadata.push({
							filePath,
							startLine: chunk.startLine,
							endLine: chunk.endLine,
						})
					})
				} catch (error) {
					// Skip files that can't be read
					continue
				}
			}

			if (codeChunks.length === 0) {
				return []
			}

			// Perform semantic search
			const semanticResults = await this.performSemanticSearch(query, codeChunks, maxResults)

			// Convert to ContextResult format
			return semanticResults.map(result => ({
				type: "code" as const,
				content: result.content,
				filePath: chunkMetadata[result.index]?.filePath,
				score: result.score,
				metadata: {
					semanticScore: result.score,
					startLine: chunkMetadata[result.index]?.startLine,
					endLine: chunkMetadata[result.index]?.endLine,
					searchType: "semantic",
				},
			}))

		} catch (error) {
			console.error("Semantic context retrieval failed:", error)
			return []
		}
	}

	/**
	 * Extract meaningful code chunks from file content
	 */
	private extractCodeChunks(
		content: string,
		filePath: string
	): Array<{ content: string; startLine: number; endLine: number }> {
		const lines = content.split('\n')
		const chunks: Array<{ content: string; startLine: number; endLine: number }> = []
		const chunkSize = 50 // Lines per chunk
		const overlap = 10 // Overlapping lines between chunks

		for (let i = 0; i < lines.length; i += chunkSize - overlap) {
			const endLine = Math.min(i + chunkSize, lines.length)
			const chunkLines = lines.slice(i, endLine)

			// Skip empty or very small chunks
			if (chunkLines.filter(line => line.trim()).length < 5) {
				continue
			}

			const chunkContent = chunkLines.join('\n')
			chunks.push({
				content: `File: ${filePath}\nLines ${i + 1}-${endLine}:\n\n${chunkContent}`,
				startLine: i + 1,
				endLine: endLine,
			})
		}

		return chunks
	}

	/**
	 * Get code files from workspace
	 */
	private async getWorkspaceCodeFiles(): Promise<string[]> {
		const codeExtensions = ['.ts', '.js', '.tsx', '.jsx', '.py', '.java', '.cpp', '.c', '.cs', '.go', '.rs', '.php', '.rb', '.swift', '.kt']
		const files: string[] = []

		const scanDirectory = async (dirPath: string, depth: number = 0): Promise<void> => {
			if (depth > 3) return // Limit recursion depth

			try {
				const entries = await fs.readdir(dirPath, { withFileTypes: true })

				for (const entry of entries) {
					const fullPath = path.join(dirPath, entry.name)

					if (entry.isDirectory()) {
						// Skip common non-code directories
						if (!['node_modules', '.git', 'dist', 'build', '__pycache__', '.vscode'].includes(entry.name)) {
							await scanDirectory(fullPath, depth + 1)
						}
					} else if (entry.isFile()) {
						const ext = path.extname(entry.name).toLowerCase()
						if (codeExtensions.includes(ext)) {
							files.push(fullPath)
						}
					}
				}
			} catch (error) {
				// Skip directories that can't be read
			}
		}

		await scanDirectory(this.workspacePath)
		return files
	}

	/**
	 * Perform semantic search using embeddings
	 */
	private async performSemanticSearch(
		query: string,
		codeChunks: string[],
		maxResults: number
	): Promise<Array<{ content: string; score: number; index: number }>> {
		if (!this.embeddingService) {
			return []
		}

		try {
			// Create embedding for the query
			const queryEmbeddingResponse = await this.embeddingService.createEmbeddings([query])
			if (queryEmbeddingResponse.embeddings.length === 0) {
				return []
			}
			const queryEmbedding = queryEmbeddingResponse.embeddings[0]

			// Create embeddings for code chunks (in batches for efficiency)
			const chunkEmbeddingResponse = await this.embeddingService.createEmbeddings(codeChunks)
			const chunkEmbeddings = chunkEmbeddingResponse.embeddings

			// Calculate cosine similarity scores
			const results = chunkEmbeddings.map((chunkEmbedding, index) => ({
				content: codeChunks[index],
				score: this.calculateCosineSimilarity(queryEmbedding, chunkEmbedding),
				index,
			}))

			// Sort by score and return top results
			return results
				.sort((a, b) => b.score - a.score)
				.slice(0, maxResults)
				.filter(result => result.score > 0.1) // Filter out very low similarity scores

		} catch (error) {
			console.error("Semantic search failed:", error)
			return []
		}
	}

	/**
	 * Calculate cosine similarity between two vectors
	 */
	private calculateCosineSimilarity(vectorA: number[], vectorB: number[]): number {
		if (vectorA.length !== vectorB.length) {
			throw new Error("Vectors must have the same length")
		}

		let dotProduct = 0
		let normA = 0
		let normB = 0

		for (let i = 0; i < vectorA.length; i++) {
			dotProduct += vectorA[i] * vectorB[i]
			normA += vectorA[i] * vectorA[i]
			normB += vectorB[i] * vectorB[i]
		}

		normA = Math.sqrt(normA)
		normB = Math.sqrt(normB)

		if (normA === 0 || normB === 0) {
			return 0
		}

		return dotProduct / (normA * normB)
	}

	/**
	 * Set embedding service for semantic search
	 */
	setEmbeddingService(embeddingService: AugmentEmbeddingService): void {
		this.embeddingService = embeddingService
	}

	/**
	 * Dispose of resources
	 */
	dispose(): void {
		// Clean up resources if needed
	}
}
