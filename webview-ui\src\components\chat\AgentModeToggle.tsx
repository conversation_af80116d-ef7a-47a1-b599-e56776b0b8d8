import React from "react"
import { cn } from "@/lib/utils"
import { useExtensionState } from "@/context/ExtensionStateContext"
import { vscode } from "@/utils/vscode"
import { useAppTranslation } from "@/i18n/TranslationContext"
import { StandardTooltip } from "@/components/ui"

interface AgentModeToggleProps {
	mode: string
	onModeChange: (mode: string) => void
}

export const AgentModeToggle: React.FC<AgentModeToggleProps> = ({ mode, onModeChange }) => {
	const { t } = useAppTranslation()
	const { autoApprovalEnabled } = useExtensionState()
	
	// Only show the toggle when auto-approve is enabled
	if (!autoApprovalEnabled) {
		return null
	}

	const isAgentMode = mode === "agent"

	const handleToggle = () => {
		const newMode = isAgentMode ? "chat" : "agent"
		onModeChange(newMode)
		vscode.postMessage({ type: "mode", text: newMode })
	}

	return (
		<div className="flex items-center justify-center mb-4">
			<StandardTooltip 
				content={isAgentMode 
					? t("chat:agentModeToggle.disableTooltip", "Switch to Chat mode for basic conversations")
					: t("chat:agentModeToggle.enableTooltip", "Switch to Agent mode for advanced AI capabilities")
				}
			>
				<button
					onClick={handleToggle}
					className={cn(
						"flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200",
						"border border-vscode-panel-border",
						isAgentMode
							? "bg-vscode-button-background text-vscode-button-foreground hover:bg-vscode-button-hoverBackground"
							: "bg-vscode-input-background text-vscode-foreground hover:bg-vscode-list-hoverBackground"
					)}
					aria-pressed={isAgentMode}
					data-testid="agent-mode-toggle"
				>
					<span className={cn(
						"codicon",
						isAgentMode ? "codicon-robot" : "codicon-comment-discussion"
					)} />
					<span>
						{isAgentMode 
							? t("chat:agentModeToggle.agentMode", "Agent Mode")
							: t("chat:agentModeToggle.chatMode", "Chat Mode")
						}
					</span>
					<div className={cn(
						"w-8 h-4 rounded-full transition-colors duration-200 relative",
						isAgentMode ? "bg-vscode-button-background" : "bg-vscode-input-border"
					)}>
						<div className={cn(
							"w-3 h-3 rounded-full bg-white transition-transform duration-200 absolute top-0.5",
							isAgentMode ? "translate-x-4" : "translate-x-0.5"
						)} />
					</div>
				</button>
			</StandardTooltip>
		</div>
	)
}
