export { AugmentContextEngine } from "./engine"
export { CommitLineageService } from "./commit-lineage"
export { CommitIndexer } from "./commit-indexer"
export { AugmentContextManager } from "./manager"
export { AugmentEmbeddingService } from "./embedding-service"

export type {
	ContextRetrievalOptions,
	ContextResult,
	WorkspaceIndexingStatus,
} from "./engine"

export type {
	CommitSummary,
	CommitLineageOptions,
} from "./commit-lineage"

export type {
	CommitIndexEntry,
	CommitIndexOptions,
} from "./commit-indexer"

export type {
	EnhancedContextOptions,
	EnhancedContextResult,
} from "./manager"

export type {
	EmbeddingOptions,
	EmbeddingResponse,
	EmbeddingValidationResult,
} from "./embedding-service"
